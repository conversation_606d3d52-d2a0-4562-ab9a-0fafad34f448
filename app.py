import streamlit as st
import requests
import json
from llm_integration import process_user_input
import os
from dotenv import load_dotenv
import pandas as pd
from datetime import datetime
import time

load_dotenv()

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []
if "user_info" not in st.session_state:
    st.session_state.user_info = {}
if "is_admin" not in st.session_state:
    st.session_state.is_admin = False
if "current_grievances" not in st.session_state:
    st.session_state.current_grievances = []
if "last_refresh" not in st.session_state:
    st.session_state.last_refresh = None

# API endpoints
API_URL = "http://localhost:8000"

def register_grievance(name, mobile, complaint_details):
    try:
        print("\n=== SENDING GRIEVANCE TO API ===")
        print(f"Name: {name}")
        print(f"Mobile: {mobile}")
        print(f"Complaint: {complaint_details}")
        
        response = requests.post(
            f"{API_URL}/register-grievance",
            json={
                "name": name,
                "mobile": mobile,
                "complaint_details": complaint_details
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            print("\n=== GRIEVANCE REGISTERED SUCCESSFULLY ===")
            print(f"ID: {result['id']}")
            print(f"Status: {result['status']}")
            print(f"Created at: {result['created_at']}")
            return result
        else:
            error_msg = response.json().get('detail', 'Unknown error')
            print(f"\n❌ Failed to register grievance: {error_msg}")
            st.error(f"Failed to register grievance: {error_msg}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"\n❌ API Connection Error: {str(e)}")
        st.error(f"Failed to connect to the server: {str(e)}")
        return None
    except Exception as e:
        print(f"\n❌ Unexpected Error: {str(e)}")
        st.error(f"An unexpected error occurred: {str(e)}")
        return None

def check_grievance_status(mobile):
    try:
        response = requests.get(f"{API_URL}/check-status/{mobile}")
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        st.error(f"Failed to check status: {str(e)}")
        return None

def get_all_grievances():
    try:
        response = requests.get(
            f"{API_URL}/admin/grievances",
            headers={"Authorization": "Basic YWRtaW46YWRtaW4xMjM="}
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        st.error(f"Failed to fetch grievances: {str(e)}")
        return []

def update_grievance_status(grievance_id, status):
    try:
        response = requests.put(
            f"{API_URL}/update-status/{grievance_id}",
            json={"status": status},
            headers={"Authorization": "Basic YWRtaW46YWRtaW4xMjM="}
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        st.error(f"Failed to update status: {str(e)}")
        return None

# Streamlit UI
st.title("Grievance Registration Chatbot")

# Sidebar for navigation and admin login
with st.sidebar:
    st.header("Navigation")
    page = st.radio("Select Page", ["Chat", "Admin View"])
    
    if page == "Admin View":
        st.header("Admin Login")
        username = st.text_input("Username")
        password = st.text_input("Password", type="password")
        
        if st.button("Login"):
            if username == "admin" and password == "admin123":
                st.session_state.is_admin = True
                st.success("Login successful!")
                st.rerun()
            else:
                st.error("Invalid credentials!")
        
        if st.session_state.is_admin:
            st.success("Logged in as Admin")
            if st.button("Logout"):
                st.session_state.is_admin = False
                st.rerun()

# Main content area
if page == "Chat":
    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.write(message["content"])

    # Chat input
    if prompt := st.chat_input("What would you like to do?"):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.write(prompt)

        # Process the input and get response
        response = process_user_input(prompt, [msg["content"] for msg in st.session_state.messages])
        
        # Check if we need to collect user information
        if "name" in response.lower() and "mobile" in response.lower():
            # Extract information from the response
            lines = response.split('\n')
            for line in lines:
                line = line.strip().lower()
                if line.startswith('name:'):
                    st.session_state.user_info['name'] = line.replace('name:', '').strip()
                elif line.startswith('mobile:'):
                    st.session_state.user_info['mobile'] = line.replace('mobile:', '').strip()
                elif line.startswith('complaint:'):
                    st.session_state.user_info['complaint_details'] = line.replace('complaint:', '').strip()

            # If we have all required information, register the grievance
            if all(key in st.session_state.user_info for key in ['name', 'mobile', 'complaint_details']):
                print("\n=== COLLECTED USER INFORMATION ===")
                print(f"Name: {st.session_state.user_info['name']}")
                print(f"Mobile: {st.session_state.user_info['mobile']}")
                print(f"Complaint: {st.session_state.user_info['complaint_details']}")
                
                result = register_grievance(
                    st.session_state.user_info['name'],
                    st.session_state.user_info['mobile'],
                    st.session_state.user_info['complaint_details']
                )
                
                if result:
                    response += f"\n\n✅ Your grievance has been registered successfully!\n"
                    response += f"Grievance ID: {result['id']}\n"
                    response += f"Status: {result['status']}\n"
                    response += f"Created at: {result['created_at']}"
                    st.session_state.current_grievances.append(result)
                    st.session_state.user_info = {}  # Clear user info after successful registration
                else:
                    response += f"\n\n❌ Error registering grievance. Please try again."

        # Add assistant response to chat history
        st.session_state.messages.append({"role": "assistant", "content": response})
        with st.chat_message("assistant"):
            st.write(response)

elif page == "Admin View":
    if st.session_state.is_admin:
        st.header("Admin Dashboard")
        
        # Auto-refresh every 30 seconds
        current_time = time.time()
        if (st.session_state.last_refresh is None or 
            current_time - st.session_state.last_refresh > 30):
            st.session_state.current_grievances = get_all_grievances()
            st.session_state.last_refresh = current_time
        
        # Manual refresh button
        if st.button("🔄 Refresh Data"):
            st.session_state.current_grievances = get_all_grievances()
            st.session_state.last_refresh = time.time()
        
        # Display all grievances
        st.subheader("All Grievances")
        if st.session_state.current_grievances:
            # Convert to DataFrame for better display
            df = pd.DataFrame(st.session_state.current_grievances)
            df['created_at'] = pd.to_datetime(df['created_at']).dt.strftime('%Y-%m-%d %H:%M:%S')
            df['updated_at'] = pd.to_datetime(df['updated_at']).dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # Add filters
            status_filter = st.selectbox("Filter by Status", ["All"] + list(df['status'].unique()))
            if status_filter != "All":
                df = df[df['status'] == status_filter]
            
            # Display the filtered data
            st.dataframe(df)
            
            # Update status section
            st.subheader("Update Grievance Status")
            grievance_ids = [str(g['id']) for g in st.session_state.current_grievances]
            selected_id = st.selectbox("Select Grievance ID", grievance_ids)
            
            if selected_id:
                # Show current grievance details
                current_grievance = next((g for g in st.session_state.current_grievances 
                                       if str(g['id']) == selected_id), None)
                if current_grievance:
                    st.write("Current Details:")
                    st.json(current_grievance)
                
                new_status = st.selectbox("New Status", 
                                        ["Pending", "In Progress", "Resolved", "Closed"])
                if st.button("Update Status"):
                    result = update_grievance_status(selected_id, new_status)
                    if result:
                        st.success("Status updated successfully!")
                        st.session_state.current_grievances = get_all_grievances()
                    else:
                        st.error("Failed to update status")
        else:
            st.info("No grievances found in the database.")
    else:
        st.warning("Please login as admin to view this page")

# Add a sidebar for additional information
with st.sidebar:
    st.header("About")
    st.write("""
    This chatbot helps you register and track grievances. You can:
    1. Register a new grievance
    2. Check the status of your existing grievance
    3. Get help with the process
    """)
    
    # Add a button to check status
    if st.button("Check Grievance Status"):
        if "mobile" in st.session_state.user_info:
            status = check_grievance_status(st.session_state.user_info["mobile"])
            if status:
                st.write("Status:", status.get("status", "Not found"))
            else:
                st.write("No grievance found for this mobile number")
        else:
            st.write("Please provide your mobile number first")