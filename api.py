from fastapi import FastAP<PERSON>, HTTPException, Depends, Head<PERSON>
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from mongodb_config import create_grievance, get_grievance_status, update_grievance_status, get_all_grievances
import logging
from bson import ObjectId

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI()

class GrievanceRequest(BaseModel):
    name: str
    mobile: str
    complaint_details: str

class GrievanceResponse(BaseModel):
    id: str
    name: str
    mobile: str
    complaint_details: str
    status: str
    created_at: str
    updated_at: str

class StatusUpdateRequest(BaseModel):
    status: str

def verify_admin(authorization: str = Header(...)) -> bool:
    if authorization == "Basic YWRtaW46YWRtaW4xMjM=":  # admin:admin123 in base64
        return True
    raise HTTPException(status_code=401, detail="Invalid credentials")

@app.post("/register-grievance", response_model=GrievanceResponse)
async def register_grievance_endpoint(grievance: GrievanceRequest):
    try:
        logger.info(f"=== GRIEVANCE REGISTRATION REQUEST ===")
        logger.info(f"Name: {grievance.name}")
        logger.info(f"Mobile: {grievance.mobile}")
        logger.info(f"Complaint: {grievance.complaint_details}")
        
        # Basic validation
        if not grievance.name.strip():
            raise HTTPException(status_code=400, detail="Name cannot be empty")
        if not grievance.mobile.isdigit() or len(grievance.mobile) != 10:
            raise HTTPException(status_code=400, detail="Invalid mobile number format. Please provide a 10-digit number.")
        if not grievance.complaint_details.strip():
            raise HTTPException(status_code=400, detail="Complaint details cannot be empty")
        
        result = create_grievance(
            name=grievance.name,
            mobile=grievance.mobile,
            complaint_details=grievance.complaint_details
        )
        
        if not result:
            logger.error("Failed to create grievance - verification failed")
            raise HTTPException(status_code=500, detail="Failed to create grievance - verification failed")
            
        logger.info(f"Successfully registered grievance with ID: {result['id']}")
        logger.info(f"Status: {result['status']}")
        logger.info(f"Created at: {result['created_at']}")
        
        return result
    except ValueError as ve:
        logger.error(f"Validation error: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"Error in register_grievance_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/check-status/{mobile}", response_model=GrievanceResponse)
async def check_status(mobile: str):
    try:
        logger.info(f"Checking status for mobile: {mobile}")
        result = get_grievance_status(mobile)
        if not result:
            logger.warning(f"No grievance found for mobile: {mobile}")
            raise HTTPException(status_code=404, detail="Grievance not found")
        logger.info(f"Found grievance with ID: {result['id']}")
        return result
    except Exception as e:
        logger.error(f"Error in check_status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/update-status/{grievance_id}", response_model=GrievanceResponse)
async def update_status_endpoint(grievance_id: str, status_update: StatusUpdateRequest, _: bool = Depends(verify_admin)):
    try:
        logger.info(f"Updating status for grievance ID: {grievance_id}")
        result = update_grievance_status(ObjectId(grievance_id), status_update.status)
        if not result:
            logger.warning(f"No grievance found with ID: {grievance_id}")
            raise HTTPException(status_code=404, detail="Grievance not found")
        logger.info(f"Successfully updated status for grievance ID: {grievance_id}")
        return result
    except Exception as e:
        logger.error(f"Error in update_status_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/admin/grievances", response_model=List[GrievanceResponse])
async def get_all_grievances_endpoint(_: bool = Depends(verify_admin)):
    try:
        logger.info("Fetching all grievances")
        grievances = get_all_grievances()
        logger.info(f"Retrieved {len(grievances)} grievances")
        return grievances
    except Exception as e:
        logger.error(f"Error in get_all_grievances_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 