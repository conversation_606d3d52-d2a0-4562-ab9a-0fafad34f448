import os
from dotenv import load_dotenv

load_dotenv()

# Try to import Groq, but provide fallback if it fails
try:
    from groq import Groq
    client = Groq(api_key=os.getenv("GROQ_API_KEY"))
    GROQ_AVAILABLE = True
    print("Groq client initialized successfully")
except Exception as e:
    print(f"Groq not available, using fallback: {e}")
    client = None
    GROQ_AVAILABLE = False

SYSTEM_PROMPT = """You are a helpful grievance registration assistant. Your role is to:
1. Help users register their grievances by collecting necessary information
2. Provide status updates for existing grievances
3. Maintain a professional and empathetic tone
4. Guide users through the grievance registration process

When registering a grievance, collect:
- Name
- Mobile number
- Complaint details

For status checks, ask for the mobile number if not provided."""

def get_chat_response(messages):
    if not GROQ_AVAILABLE or client is None:
        # Enhanced fallback response that mimics the original LLM behavior
        user_message = messages[-1]["content"].lower() if messages else ""

        # Check if user is providing information in the expected format
        if "name:" in user_message and "mobile:" in user_message and "complaint:" in user_message:
            # Extract the information
            lines = user_message.split('\n')
            name = ""
            mobile = ""
            complaint = ""

            for line in lines:
                line = line.strip()
                if line.lower().startswith('name:'):
                    name = line.split(':', 1)[1].strip()
                elif line.lower().startswith('mobile:'):
                    mobile = line.split(':', 1)[1].strip()
                elif line.lower().startswith('complaint:'):
                    complaint = line.split(':', 1)[1].strip()

            if name and mobile and complaint:
                return f"""Thank you for providing the information. I have collected:

Name: {name}
Mobile: {mobile}
Complaint: {complaint}

I will now register your grievance."""

        # Check if user is asking to register a grievance
        elif any(word in user_message for word in ["register", "complaint", "grievance", "problem", "issue", "help"]):
            return """I'll help you register a grievance. Please provide your information in the following format:

Name: [Your full name]
Mobile: [Your 10-digit mobile number]
Complaint: [Describe your issue in detail]

Please provide all three pieces of information so I can register your grievance."""

        # Check if user is asking for status
        elif any(word in user_message for word in ["status", "check", "track"]):
            return "To check your grievance status, please provide your mobile number."

        # Default greeting
        else:
            return """Hello! I'm here to help you with grievance registration. I can:

1. Register a new grievance - Just tell me about your problem
2. Check the status of an existing grievance

How can I assist you today?"""

    try:
        chat_completion = client.chat.completions.create(
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                *messages
            ],
            model="llama-3.3-70b-versatile",
            temperature=0.7,
            max_tokens=1024,
        )
        return chat_completion.choices[0].message.content
    except Exception as e:
        return f"Error: {str(e)}"

def process_user_input(user_input, conversation_history):
    messages = [
        {"role": "user", "content": msg} if i % 2 == 0 else {"role": "assistant", "content": msg}
        for i, msg in enumerate(conversation_history)
    ]
    messages.append({"role": "user", "content": user_input})
    
    response = get_chat_response(messages)
    return response 