import os
from dotenv import load_dotenv

load_dotenv()

# Try to import Groq, but provide fallback if it fails
try:
    from groq import Groq
    client = Groq(api_key=os.getenv("GROQ_API_KEY"))
    GROQ_AVAILABLE = True
    print("Groq client initialized successfully")
except Exception as e:
    print(f"Groq not available, using fallback: {e}")
    client = None
    GROQ_AVAILABLE = False

SYSTEM_PROMPT = """You are an intelligent grievance registration assistant. Your role is to:
1. Help users register their grievances by collecting necessary information through natural conversation
2. Extract information from user messages intelligently
3. Provide status updates for existing grievances
4. Maintain a professional and empathetic tone
5. Guide users through the grievance registration process

INFORMATION EXTRACTION RULES:
- Extract names from phrases like "My name is <PERSON>", "I am <PERSON>", "This is <PERSON> calling"
- Extract mobile numbers from any 10-digit number mentioned
- Extract complaints from descriptions of problems, issues, or concerns
- Be intelligent about context - if someone says "I have a water problem", that's a complaint

RESPONSE FORMAT RULES:
When you have collected all three pieces of information (name, mobile, complaint), you MUST format your response to include these exact lines:

Name: [extracted name]
Mobile: [extracted mobile number]
Complaint: [extracted complaint details]

CONVERSATION FLOW:
- If user mentions a problem but no name/mobile, ask for missing information
- If user provides name but no mobile/complaint, ask for what's missing
- If user provides mobile but no name/complaint, ask for what's missing
- Be conversational and natural, don't just ask for a list
- Acknowledge what they've told you and ask for what's still needed

EXAMPLES:
User: "Hi, I'm John and I have a water supply issue"
You: "Hello John! I understand you're having a water supply issue. To register your grievance, I'll need your mobile number as well."

User: "My number is 9876543210"
You: "Thank you! I have your name as John and mobile as 9876543210. Could you please provide more details about the water supply issue you're experiencing?"

User: "There's no water in my area for 3 days"
You: "I understand the situation. Let me register your grievance:

Name: John
Mobile: 9876543210
Complaint: No water supply in area for 3 days"

Always be helpful and extract information intelligently from natural conversation."""

def extract_information_from_text(text):
    """Extract name, mobile, and complaint from natural text"""
    import re

    # Handle case where text might not be a string
    if not isinstance(text, str):
        return None, None, None

    name = None
    mobile = None
    complaint = None

    # Extract mobile number (10 digits)
    mobile_pattern = r'\b\d{10}\b'
    mobile_match = re.search(mobile_pattern, text)
    if mobile_match:
        mobile = mobile_match.group()

    # Extract name patterns (more precise)
    name_patterns = [
        r'my name is ([a-zA-Z\s]{2,30})(?:\s+and|\s*,|\s*\.|\s*$)',
        r'i am ([a-zA-Z\s]{2,30})(?:\s+and|\s*,|\s*\.|\s*$)',
        r'this is ([a-zA-Z\s]{2,30})(?:\s+calling|\s+speaking|\s*,|\s*\.|\s*$)',
        r'name:\s*([a-zA-Z\s]{2,30})(?:\s*,|\s*\.|\s*$)',
        r'i\'m ([a-zA-Z\s]{2,30})(?:\s+and|\s*,|\s*\.|\s*$)'
    ]

    for pattern in name_patterns:
        match = re.search(pattern, text.lower())
        if match:
            extracted_name = match.group(1).strip()
            # Clean up the name - remove common words that might be captured
            words_to_remove = ['and', 'have', 'has', 'with', 'having', 'facing', 'experiencing']
            name_words = extracted_name.split()
            clean_name_words = []
            for word in name_words:
                if word.lower() not in words_to_remove:
                    clean_name_words.append(word)
                else:
                    break  # Stop at first unwanted word
            if clean_name_words:
                name = ' '.join(clean_name_words).title()
                break

    # Extract complaint (look for problem descriptions)
    complaint_keywords = ['problem', 'issue', 'complaint', 'trouble', 'not working', 'broken', 'damaged', 'outage', 'supply', 'repair', 'fix', 'water', 'electricity', 'road', 'garbage', 'drainage']
    if any(keyword in text.lower() for keyword in complaint_keywords):
        # If it's a complaint, use the whole message as complaint context
        complaint = text.strip()

    return name, mobile, complaint

def get_chat_response(messages):
    if not GROQ_AVAILABLE or client is None:
        # Enhanced fallback response with intelligent information extraction
        user_message = messages[-1]["content"] if messages else ""
        user_message_lower = user_message.lower()

        # Extract information from current message
        name, mobile, complaint = extract_information_from_text(user_message)

        # Also check previous USER messages for context (not assistant messages)
        collected_name = None
        collected_mobile = None
        collected_complaint = None

        for msg in messages[:-1]:  # Check all previous messages
            # Only extract from user messages, not assistant messages
            if isinstance(msg, dict) and msg.get('role') == 'user':
                msg_content = msg.get('content', '')
                prev_name, prev_mobile, prev_complaint = extract_information_from_text(msg_content)
                if prev_name and not collected_name:
                    collected_name = prev_name
                if prev_mobile and not collected_mobile:
                    collected_mobile = prev_mobile
                if prev_complaint and not collected_complaint:
                    collected_complaint = prev_complaint

        # Use current extraction or previous collection
        final_name = name or collected_name
        final_mobile = mobile or collected_mobile
        final_complaint = complaint or collected_complaint

        # Check if we have all information
        if final_name and final_mobile and final_complaint:
            return f"""Thank you! I have collected all the information needed:

Name: {final_name}
Mobile: {final_mobile}
Complaint: {final_complaint}

I will now register your grievance."""

        # Partial information - ask for what's missing
        elif final_name and final_mobile and not final_complaint:
            return f"Thank you {final_name}! I have your name and mobile number ({final_mobile}). Could you please describe the issue or problem you'd like to report?"

        elif final_name and final_complaint and not final_mobile:
            return f"Thank you {final_name}! I understand your concern about: {final_complaint}. Could you please provide your mobile number for registration?"

        elif final_mobile and final_complaint and not final_name:
            return f"I have your mobile number ({final_mobile}) and understand your issue: {final_complaint}. Could you please tell me your name?"

        elif final_name and not final_mobile and not final_complaint:
            return f"Hello {final_name}! To register your grievance, I'll need your mobile number and details about the issue you're facing."

        elif final_mobile and not final_name and not final_complaint:
            return f"I have your mobile number ({final_mobile}). Could you please tell me your name and describe the issue you'd like to report?"

        elif final_complaint and not final_name and not final_mobile:
            return f"I understand you're facing this issue: {final_complaint}. To register your grievance, I'll need your name and mobile number."

        # Check if user is asking for status
        elif any(word in user_message_lower for word in ["status", "check", "track"]):
            return "To check your grievance status, please provide your mobile number."

        # Check if user is asking to register or has a problem
        elif any(word in user_message_lower for word in ["register", "complaint", "grievance", "problem", "issue", "help", "trouble"]):
            return """I'll help you register a grievance. Please tell me:

1. Your name
2. Your mobile number
3. Details about the issue you're facing

You can provide this information naturally in conversation - for example: "Hi, I'm John, my number is 9876543210, and I have a water supply problem in my area." """

        # Default greeting
        else:
            return """Hello! I'm here to help you with grievance registration. I can:

1. Register a new grievance - Just tell me your name, mobile number, and describe your problem
2. Check the status of an existing grievance

How can I assist you today?"""

    try:
        chat_completion = client.chat.completions.create(
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                *messages
            ],
            model="llama-3.3-70b-versatile",
            temperature=0.7,
            max_tokens=1024,
        )
        return chat_completion.choices[0].message.content
    except Exception as e:
        return f"Error: {str(e)}"

def process_user_input(user_input, conversation_history):
    """Process user input with conversation context"""
    # Build conversation messages with proper role alternation
    messages = []

    # Add conversation history
    for i, msg in enumerate(conversation_history[:-1]):  # Exclude the current user input
        if i % 2 == 0:
            messages.append({"role": "user", "content": msg})
        else:
            messages.append({"role": "assistant", "content": msg})

    # Add current user input
    messages.append({"role": "user", "content": user_input})

    # Get response from LLM or fallback
    response = get_chat_response(messages)

    return response